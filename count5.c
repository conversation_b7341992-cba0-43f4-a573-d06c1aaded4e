// write  a program to count how many times the digit 5 appears in given string
#include<stdio.h>
int main(){
    char str[1000];
    int count = 0;
    int i;

    printf("Enter a string: ");
       fgets(str, sizeof(str), stdin);  

    printf("You entered: %s\n", str);  // Debug output

    // Loop through each character in the string
    for(i = 0; str[i] != '\0'; i++){
        if(str[i] == '5'){
            count++;
        }
    }

    printf("Count of digit 5 is: %d\n", count);
    return 0;
}